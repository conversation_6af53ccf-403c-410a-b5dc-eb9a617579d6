class ProductCatalogueParams {
  final String? productCreationFormat;
  final List<Product>? products;

  ProductCatalogueParams({
    this.productCreationFormat,
    this.products,
  });

  Map<String, dynamic> toJson() {
    return {
      "product_creation_format": productCreationFormat,
      "products": products?.map((e) => e.toJson()).toList(),
    };
  }
}

class Product {
  final String? name;
  final String? sku;
  final String? code;
  final int? unitCostPrice;
  final int? unitRetailPrice;
  final int? quantity;
  final int? reorderValue;
  final String? description;
  final String? tags;
  final String? catalogueId;
  final int? currentPrice;
  final InventoryMedia? media;

  Product({
    this.name,
    this.sku,
    this.code,
    this.unitCostPrice,
    this.unitRetailPrice,
    this.quantity,
    this.reorderValue,
    this.description,
    this.tags,
    this.catalogueId,
    this.currentPrice,
    this.media,
  });

  Map<String, dynamic> toJson() {
    return {
      "name": name,
      "SKU": sku,
      "code": code,
      "unit_cost_price": unitCostPrice,
      "unit_retail_price": unitRetailPrice,
      "quantity": quantity,
      "reorder_value": reorderValue,
      "description": description,
      "tags": tags,
      "catalogue_id": catalogueId,
      "current_price": currentPrice,
      "media": media?.toJson(),
    };
  }
}

class InventoryMedia {
  final String? coverImageUrl;
  final String? productImageUrl;

  InventoryMedia({
    this.coverImageUrl,
    this.productImageUrl,
  });

  Map<String, dynamic> toJson() {
    return {
      "cover_image_url": coverImageUrl,
      "product_image_url": productImageUrl,
    };
  }
}
