import '../models/local/product_catalogue_params.dart';

class ProductValidationUtils {
  /// Validates if a single product catalogue with status is complete
  static bool isProductComplete(ProductCatalogueWithStatus productStatus) {
    return productStatus.formData?.isComplete ?? false;
  }

  /// Validates if all products in a list are complete
  static bool areAllProductsComplete(List<ProductCatalogueWithStatus> products) {
    if (products.isEmpty) return false;
    
    return products.every((product) => isProductComplete(product));
  }

  /// Gets the count of completed products
  static int getCompletedProductsCount(List<ProductCatalogueWithStatus> products) {
    return products.where((product) => isProductComplete(product)).length;
  }

  /// Gets the count of incomplete products
  static int getIncompleteProductsCount(List<ProductCatalogueWithStatus> products) {
    return products.where((product) => !isProductComplete(product)).length;
  }

  /// Validates individual form fields
  static String? validateSellingUnit(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Selling unit is required';
    }
    return null;
  }

  static String? validateCostPrice(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Cost price is required';
    }
    
    final price = int.tryParse(value);
    if (price == null || price <= 0) {
      return 'Please enter a valid cost price';
    }
    
    return null;
  }

  static String? validateSellingPrice(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Selling price is required';
    }
    
    final price = int.tryParse(value);
    if (price == null || price <= 0) {
      return 'Please enter a valid selling price';
    }
    
    return null;
  }

  static String? validateSku(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'SKU is required';
    }
    
    if (value.trim().length < 3) {
      return 'SKU must be at least 3 characters long';
    }
    
    return null;
  }

  static String? validateStockQuantity(String? value) {
    if (value != null && value.trim().isNotEmpty) {
      final quantity = int.tryParse(value);
      if (quantity == null || quantity < 0) {
        return 'Please enter a valid stock quantity';
      }
    }
    return null;
  }

  static String? validateReorderLevel(String? value) {
    if (value != null && value.trim().isNotEmpty) {
      final level = int.tryParse(value);
      if (level == null || level < 0) {
        return 'Please enter a valid reorder level';
      }
    }
    return null;
  }

  static String? validateDiscountPrice(String? value, int? sellingPrice) {
    if (value != null && value.trim().isNotEmpty) {
      final discount = int.tryParse(value);
      if (discount == null || discount < 0) {
        return 'Please enter a valid discount price';
      }
      
      if (sellingPrice != null && discount >= sellingPrice) {
        return 'Discount price must be less than selling price';
      }
    }
    return null;
  }

  /// Gets validation errors for a ProductFormData object
  static List<String> getValidationErrors(ProductFormData? formData) {
    if (formData == null) {
      return ['Product form data is missing'];
    }

    List<String> errors = [];

    final sellingUnitError = validateSellingUnit(formData.sellingUnit);
    if (sellingUnitError != null) errors.add(sellingUnitError);

    final costPriceError = validateCostPrice(formData.costPrice?.toString());
    if (costPriceError != null) errors.add(costPriceError);

    final sellingPriceError = validateSellingPrice(formData.sellingPrice?.toString());
    if (sellingPriceError != null) errors.add(sellingPriceError);

    final skuError = validateSku(formData.sku);
    if (skuError != null) errors.add(skuError);

    final stockQuantityError = validateStockQuantity(formData.stockQuantity?.toString());
    if (stockQuantityError != null) errors.add(stockQuantityError);

    final reorderLevelError = validateReorderLevel(formData.reorderLevel?.toString());
    if (reorderLevelError != null) errors.add(reorderLevelError);

    final discountPriceError = validateDiscountPrice(
      formData.discountPrice?.toString(), 
      formData.sellingPrice
    );
    if (discountPriceError != null) errors.add(discountPriceError);

    return errors;
  }

  /// Checks if a product can be saved (has minimum required fields)
  static bool canSaveProduct(ProductFormData? formData) {
    if (formData == null) return false;
    
    // At minimum, we need selling unit, cost price, selling price, and SKU
    return formData.sellingUnit != null &&
           formData.sellingUnit!.isNotEmpty &&
           formData.costPrice != null &&
           formData.sellingPrice != null &&
           formData.sku != null &&
           formData.sku!.isNotEmpty;
  }
}
