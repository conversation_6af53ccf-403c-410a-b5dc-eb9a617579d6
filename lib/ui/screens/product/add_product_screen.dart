// ignore_for_file: use_build_context_synchronously

import 'package:builders_konnect/core/core.dart';
import 'package:builders_konnect/ui/components/components.dart';
import 'package:flutter/services.dart';

class AddProductScreen extends ConsumerStatefulWidget {
  const AddProductScreen(
      {super.key,
      required this.productWithStatus,
      required this.onProductUpdated});

  final ProductCatalogueWithStatus productWithStatus;
  final Function(ProductCatalogueWithStatus) onProductUpdated;

  @override
  ConsumerState<AddProductScreen> createState() => _AddProductScreenState();
}

class _AddProductScreenState extends ConsumerState<AddProductScreen> {
  final sellingUnitC = TextEditingController();
  final stockQuantityC = TextEditingController();
  final quantityPerSellingUnitC = TextEditingController();
  final minimumOrderQuantityC = TextEditingController();
  final weightPerSellingUnitC = TextEditingController();
  final reorderLevelC = TextEditingController();
  final costPriceC = TextEditingController();
  final sellingPriceC = TextEditingController();
  final discountPriceC = TextEditingController();
  final productTagsC = TextEditingController();
  final storeKeepingUnitC = TextEditingController();
  final skuC = TextEditingController();
  final productDescriptionC = TextEditingController();

  String? selectedSellingUnit;
  String? selectedSubUnit;

  late ProductCatalogueWithStatus currentProductStatus;

  @override
  void initState() {
    super.initState();
    currentProductStatus = widget.productWithStatus;
    _initializeFormData();
  }

  void _initializeFormData() {
    final formData = currentProductStatus.formData;
    if (formData != null) {
      selectedSellingUnit = formData.sellingUnit;
      selectedSubUnit = formData.subUnit;
      stockQuantityC.text = formData.stockQuantity?.toString() ?? '';
      quantityPerSellingUnitC.text =
          formData.quantityPerSellingUnit?.toString() ?? '';
      minimumOrderQuantityC.text =
          formData.minimumOrderQuantity?.toString() ?? '';
      weightPerSellingUnitC.text =
          formData.weightPerSellingUnit?.toString() ?? '';
      reorderLevelC.text = formData.reorderLevel?.toString() ?? '';
      costPriceC.text = formData.costPrice?.toString() ?? '';
      sellingPriceC.text = formData.sellingPrice?.toString() ?? '';
      discountPriceC.text = formData.discountPrice?.toString() ?? '';
      productTagsC.text = formData.tags ?? '';
      skuC.text = formData.sku ?? '';
      productDescriptionC.text = formData.description ?? '';
    }
  }

  void _saveProductData() {
    // Validate required fields
    final validationErrors = <String>[];

    if (selectedSellingUnit == null || selectedSellingUnit!.isEmpty) {
      validationErrors.add('Selling unit is required');
    }

    if (costPriceC.text.isEmpty) {
      validationErrors.add('Cost price is required');
    } else {
      final costPrice = int.tryParse(costPriceC.text);
      if (costPrice == null || costPrice <= 0) {
        validationErrors.add('Please enter a valid cost price');
      }
    }

    if (sellingPriceC.text.isEmpty) {
      validationErrors.add('Selling price is required');
    } else {
      final sellingPrice = int.tryParse(sellingPriceC.text);
      if (sellingPrice == null || sellingPrice <= 0) {
        validationErrors.add('Please enter a valid selling price');
      }
    }

    if (skuC.text.isEmpty) {
      validationErrors.add('SKU is required');
    } else if (skuC.text.length < 3) {
      validationErrors.add('SKU must be at least 3 characters long');
    }

    // Validate discount price if provided
    if (discountPriceC.text.isNotEmpty) {
      final discountPrice = int.tryParse(discountPriceC.text);
      final sellingPrice = int.tryParse(sellingPriceC.text);

      if (discountPrice == null || discountPrice < 0) {
        validationErrors.add('Please enter a valid discount price');
      } else if (sellingPrice != null && discountPrice >= sellingPrice) {
        validationErrors.add('Discount price must be less than selling price');
      }
    }

    if (validationErrors.isNotEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
              'Please fix the following errors:\n${validationErrors.join('\n')}'),
          backgroundColor: Colors.red,
          duration: Duration(seconds: 4),
        ),
      );
      return;
    }

    final formData = ProductFormData(
      sellingUnit: selectedSellingUnit,
      subUnit: selectedSubUnit,
      stockQuantity: int.tryParse(stockQuantityC.text),
      quantityPerSellingUnit: int.tryParse(quantityPerSellingUnitC.text),
      minimumOrderQuantity: int.tryParse(minimumOrderQuantityC.text),
      weightPerSellingUnit: int.tryParse(weightPerSellingUnitC.text),
      reorderLevel: int.tryParse(reorderLevelC.text),
      costPrice: int.tryParse(costPriceC.text),
      sellingPrice: int.tryParse(sellingPriceC.text),
      discountPrice: int.tryParse(discountPriceC.text),
      tags: productTagsC.text.isNotEmpty ? productTagsC.text : null,
      sku: skuC.text.isNotEmpty ? skuC.text : null,
      description:
          productDescriptionC.text.isNotEmpty ? productDescriptionC.text : null,
    );

    final updatedProductStatus = currentProductStatus.copyWith(
      formData: formData,
      isComplete: formData.isComplete,
    );

    setState(() {
      currentProductStatus = updatedProductStatus;
    });

    widget.onProductUpdated(updatedProductStatus);

    // Show success message
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(formData.isComplete
            ? 'Product information saved successfully!'
            : 'Product information saved. Please complete all required fields.'),
        backgroundColor: formData.isComplete ? Colors.green : Colors.orange,
      ),
    );

    Navigator.pop(context);
  }

  void _removeProduct() {
    Navigator.pop(context, 'remove');
  }

  @override
  void dispose() {
    sellingUnitC.dispose();
    stockQuantityC.dispose();
    quantityPerSellingUnitC.dispose();
    minimumOrderQuantityC.dispose();
    weightPerSellingUnitC.dispose();
    reorderLevelC.dispose();
    costPriceC.dispose();
    sellingPriceC.dispose();
    discountPriceC.dispose();
    productTagsC.dispose();
    storeKeepingUnitC.dispose();
    skuC.dispose();
    productDescriptionC.dispose();

    super.dispose();
  }

  void _showSubUnitModal(BuildContext context, MeasurementCategory unit) {
    ModalWrapper.bottomSheet(
      context: context,
      widget: UnitModal(
        title: unit.name,
        options: unit.value
            .map(
              (subUnit) => UnitModalOption(
                title: subUnit,
                onTap: () {
                  Navigator.pop(context);
                  setState(() {
                    selectedSubUnit = subUnit;
                  });
                },
              ),
            )
            .toList(),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;
    return Scaffold(
        appBar: CustomAppbar(
          title: "Add Product Information",
        ),
        body: ListView(
          padding: EdgeInsets.only(
            left: Sizer.width(16),
            right: Sizer.width(16),
            bottom: Sizer.height(50),
          ),
          children: [
            YBox(16),
            Container(
              padding: EdgeInsets.all(Sizer.radius(16)),
              decoration: BoxDecoration(
                color: colorScheme.white,
                borderRadius: BorderRadius.circular(Sizer.radius(6)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    padding: EdgeInsets.symmetric(
                      vertical: Sizer.height(8),
                      horizontal: Sizer.width(16),
                    ),
                    decoration: BoxDecoration(
                      color: AppColors.neutral3,
                      borderRadius: BorderRadius.circular(Sizer.radius(4)),
                    ),
                    child: Row(
                      children: [
                        SizedBox(
                          width: Sizer.width(44),
                          height: Sizer.height(44),
                          child: MyCachedNetworkImage(
                            imageUrl: currentProductStatus
                                    .catalogueModel.primaryMediaUrl ??
                                "",
                            fit: BoxFit.cover,
                          ),
                        ),
                        XBox(8),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                currentProductStatus.catalogueModel.name ?? "",
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                                style: textTheme.text16?.medium,
                              ),
                              Text(
                                currentProductStatus
                                        .catalogueModel.productType?.name ??
                                    "",
                                style: textTheme.text14?.copyWith(
                                  color: colorScheme.black45,
                                ),
                              )
                            ],
                          ),
                        )
                      ],
                    ),
                  ),
                  YBox(16),
                  RichText(
                    text: TextSpan(
                      style: textTheme.text14,
                      children: [
                        TextSpan(
                          text: "All asterisk (",
                        ),
                        TextSpan(
                          text: "*",
                          style: textTheme.text14?.medium.copyWith(
                            color: Colors.red,
                          ),
                        ),
                        TextSpan(
                          text: ") are required fields",
                        ),
                      ],
                    ),
                  ),
                  YBox(16),
                  CustomTextField(
                    controller: sellingUnitC,
                    labelText: 'Selling Units',
                    hintText:
                        selectedSubUnit != null && selectedSellingUnit != null
                            ? '$selectedSellingUnit - $selectedSubUnit'
                            : selectedSellingUnit ?? 'Select unit type',
                    showLabelHeader: true,
                    readOnly: true,
                    showSuffixIcon: true,
                    onTap: () {
                      ModalWrapper.bottomSheet(
                        context: context,
                        widget: UnitModal(
                          title: "Selling Units",
                          options: sellingUnits
                              .map(
                                (unit) => UnitModalOption(
                                  title: unit.name,
                                  onTap: () {
                                    Navigator.pop(context);
                                    setState(() {
                                      selectedSellingUnit = unit.name;
                                      selectedSubUnit =
                                          null; // Reset sub-unit when main unit changes
                                    });

                                    // Show sub-unit modal if the selected unit has sub-units
                                    if (unit.hasSubUnits) {
                                      _showSubUnitModal(context, unit);
                                    }
                                  },
                                ),
                              )
                              .toList(),
                        ),
                      );
                    },
                  ),
                  YBox(16),
                  CustomTextField(
                    controller: stockQuantityC,
                    labelText: 'Stock Quantity',
                    hintText: 'Enter stock quantity',
                    showLabelHeader: true,
                    keyboardType: TextInputType.number,
                    inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                  ),
                  YBox(16),
                  CustomTextField(
                    controller: quantityPerSellingUnitC,
                    labelText: 'Quantity per Selling Unit',
                    hintText: 'Enter quantity per selling unit',
                    showLabelHeader: true,
                    keyboardType: TextInputType.number,
                    inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                  ),
                  YBox(16),
                  CustomTextField(
                    controller: minimumOrderQuantityC,
                    labelText: 'Minimum Order Quantity',
                    hintText: 'Enter minimum order quantity',
                    showLabelHeader: true,
                    keyboardType: TextInputType.number,
                    inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                  ),
                  YBox(16),
                  CustomTextField(
                    controller: weightPerSellingUnitC,
                    labelText: 'Weight per Selling Unit',
                    hintText: 'Enter weight per selling unit',
                    showLabelHeader: true,
                    keyboardType: TextInputType.number,
                    inputFormatters: [
                      FilteringTextInputFormatter.digitsOnly,
                    ],
                    suffixIcon: SuffixBox(text: "kg"),
                  ),
                  YBox(16),
                  CustomTextField(
                    controller: reorderLevelC,
                    labelText: 'Reorder Level',
                    hintText: 'Enter reorder level',
                    showLabelHeader: true,
                    keyboardType: TextInputType.number,
                    inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                  ),
                  YBox(16),
                  CustomTextField(
                    controller: costPriceC,
                    labelText: 'Cost Price *',
                    hintText: '00.00',
                    showLabelHeader: true,
                    keyboardType: TextInputType.number,
                    inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                    prefixIcon: Padding(
                      padding: EdgeInsets.all(Sizer.radius(10)),
                      child: SvgPicture.asset(
                        AppSvgs.naira,
                        fit: BoxFit.cover,
                      ),
                    ),
                  ),
                  YBox(16),
                  CustomTextField(
                    controller: sellingPriceC,
                    labelText: 'Selling Price *',
                    hintText: '00.00',
                    showLabelHeader: true,
                    keyboardType: TextInputType.number,
                    inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                    prefixIcon: Padding(
                      padding: EdgeInsets.all(Sizer.radius(10)),
                      child: SvgPicture.asset(
                        AppSvgs.naira,
                        fit: BoxFit.cover,
                      ),
                    ),
                  ),
                  YBox(16),
                  CustomTextField(
                    controller: discountPriceC,
                    labelText: 'Discount Price',
                    optionalText: "(optional)",
                    hintText: '00.00',
                    showLabelHeader: true,
                    keyboardType: TextInputType.number,
                    inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                    prefixIcon: Padding(
                      padding: EdgeInsets.all(Sizer.radius(10)),
                      child: SvgPicture.asset(
                        AppSvgs.naira,
                        fit: BoxFit.cover,
                      ),
                    ),
                  ),
                  YBox(16),
                  CustomTextField(
                    controller: productTagsC,
                    labelText: 'Product Tags',
                    hintText: 'Enter product tags',
                    showLabelHeader: true,
                  ),
                  Text(
                    "This will help customers find your product in the marketplace.",
                    style: textTheme.text14?.copyWith(
                      color: colorScheme.black45,
                    ),
                  ),
                  YBox(16),
                  CustomTextField(
                    controller: skuC,
                    labelText: 'Store Keeping Unit (SKU) *',
                    hintText: 'TL-12346-IB',
                    showLabelHeader: true,
                  ),
                  YBox(16),
                  CustomTextField(
                    controller: productDescriptionC,
                    labelText: 'Description',
                    hintText: 'Enter product description',
                    showLabelHeader: true,
                    maxLines: 3,
                  ),
                  YBox(16),
                  Text(
                    "Product Images",
                    style: textTheme.text14,
                  ),
                  YBox(8),
                  InkWell(
                    onTap: () {
                      Navigator.pushNamed(context, RoutePath.viewUploadScreen);
                    },
                    child: SizedBox(
                      height: Sizer.height(104),
                      width: Sizer.screenWidth,
                      child: SvgPicture.asset(
                        AppSvgs.uploadImg,
                        fit: BoxFit.cover,
                      ),
                    ),
                  ),
                  YBox(4),
                  Text(
                    "Recommended file size is less than 2MB. JEPG, PNG formats only",
                    style: textTheme.text14?.copyWith(
                      color: colorScheme.black45,
                    ),
                  ),
                  YBox(32),
                  Row(
                    children: [
                      Expanded(
                        child: CustomBtn.withChild(
                          isOutline: true,
                          onTap: _removeProduct,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              SvgPicture.asset(
                                AppSvgs.trash,
                                height: Sizer.height(16),
                              ),
                              XBox(10),
                              Text(
                                "Remove",
                                style: textTheme.text16?.copyWith(
                                  color: AppColors.red2D,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      XBox(16),
                      Expanded(
                        child: CustomBtn.solid(
                          text: "Save",
                          onTap: _saveProductData,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ));
  }
}

class SuffixBox extends StatelessWidget {
  const SuffixBox({
    super.key,
    required this.text,
  });

  final String text;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(Sizer.radius(10)),
      decoration: BoxDecoration(
        border: Border(
          left: BorderSide(
            color: AppColors.neutral5,
          ),
        ),
      ),
      child: Text(text),
    );
  }
}
