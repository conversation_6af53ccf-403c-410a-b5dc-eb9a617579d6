// ignore_for_file: use_build_context_synchronously

import 'package:builders_konnect/core/core.dart';
import 'package:builders_konnect/ui/components/components.dart';
import 'package:flutter/services.dart';

class AddProductScreen extends ConsumerStatefulWidget {
  const AddProductScreen({super.key, required this.product});

  final CatalogueModel product;

  @override
  ConsumerState<AddProductScreen> createState() => _AddProductScreenState();
}

class _AddProductScreenState extends ConsumerState<AddProductScreen> {
  final sellingUnitC = TextEditingController();
  final stockQuantityC = TextEditingController();
  final quantityPerSellingUnitC = TextEditingController();
  final minimumOrderQuantityC = TextEditingController();
  final weightPerSellingUnitC = TextEditingController();
  final reorderLevelC = TextEditingController();
  final costPriceC = TextEditingController();
  final sellingPriceC = TextEditingController();
  final discountPriceC = TextEditingController();
  final productTagsC = TextEditingController();
  final storeKeepingUnitC = TextEditingController();
  final skuC = TextEditingController();
  final productDescriptionC = TextEditingController();

  String? selectedSellingUnit;
  String? selectedSubUnit;

  @override
  void dispose() {
    sellingUnitC.dispose();
    stockQuantityC.dispose();
    quantityPerSellingUnitC.dispose();
    minimumOrderQuantityC.dispose();
    weightPerSellingUnitC.dispose();
    reorderLevelC.dispose();
    costPriceC.dispose();
    sellingPriceC.dispose();
    discountPriceC.dispose();
    productTagsC.dispose();
    storeKeepingUnitC.dispose();
    skuC.dispose();
    productDescriptionC.dispose();

    super.dispose();
  }

  void _showSubUnitModal(BuildContext context, MeasurementCategory unit) {
    ModalWrapper.bottomSheet(
      context: context,
      widget: UnitModal(
        title: unit.name,
        options: unit.value
            .map(
              (subUnit) => UnitModalOption(
                title: subUnit,
                onTap: () {
                  Navigator.pop(context);
                  setState(() {
                    selectedSubUnit = subUnit;
                  });
                },
              ),
            )
            .toList(),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;
    final productVm = ref.watch(productInventoryVmodel);
    return Scaffold(
        appBar: CustomAppbar(
          title: "Add Product Information",
        ),
        body: ListView(
          padding: EdgeInsets.only(
            left: Sizer.width(16),
            right: Sizer.width(16),
            bottom: Sizer.height(50),
          ),
          children: [
            YBox(16),
            Container(
              padding: EdgeInsets.all(Sizer.radius(16)),
              decoration: BoxDecoration(
                color: colorScheme.white,
                borderRadius: BorderRadius.circular(Sizer.radius(6)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    padding: EdgeInsets.symmetric(
                      vertical: Sizer.height(8),
                      horizontal: Sizer.width(16),
                    ),
                    decoration: BoxDecoration(
                      color: AppColors.neutral3,
                      borderRadius: BorderRadius.circular(Sizer.radius(4)),
                    ),
                    child: Row(
                      children: [
                        SizedBox(
                          width: Sizer.width(44),
                          height: Sizer.height(44),
                          child: MyCachedNetworkImage(
                            imageUrl: widget.product.primaryMediaUrl ?? "",
                            fit: BoxFit.cover,
                          ),
                        ),
                        XBox(8),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                widget.product.name ?? "",
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                                style: textTheme.text16?.medium,
                              ),
                              Text(
                                widget.product.productType?.name ?? "",
                                style: textTheme.text14?.copyWith(
                                  color: colorScheme.black45,
                                ),
                              )
                            ],
                          ),
                        )
                      ],
                    ),
                  ),
                  YBox(16),
                  RichText(
                    text: TextSpan(
                      style: textTheme.text14,
                      children: [
                        TextSpan(
                          text: "All asterisk (",
                        ),
                        TextSpan(
                          text: "*",
                          style: textTheme.text14?.medium.copyWith(
                            color: Colors.red,
                          ),
                        ),
                        TextSpan(
                          text: ") are required fields",
                        ),
                      ],
                    ),
                  ),
                  YBox(16),
                  CustomTextField(
                    controller: sellingUnitC,
                    labelText: 'Selling Units',
                    hintText:
                        selectedSubUnit != null && selectedSellingUnit != null
                            ? '$selectedSellingUnit - $selectedSubUnit'
                            : selectedSellingUnit ?? 'Select unit type',
                    showLabelHeader: true,
                    readOnly: true,
                    showSuffixIcon: true,
                    onTap: () {
                      ModalWrapper.bottomSheet(
                        context: context,
                        widget: UnitModal(
                          title: "Selling Units",
                          options: sellingUnits
                              .map(
                                (unit) => UnitModalOption(
                                  title: unit.name,
                                  onTap: () {
                                    Navigator.pop(context);
                                    setState(() {
                                      selectedSellingUnit = unit.name;
                                      selectedSubUnit =
                                          null; // Reset sub-unit when main unit changes
                                    });

                                    // Show sub-unit modal if the selected unit has sub-units
                                    if (unit.hasSubUnits) {
                                      _showSubUnitModal(context, unit);
                                    }
                                  },
                                ),
                              )
                              .toList(),
                        ),
                      );
                    },
                  ),
                  YBox(16),
                  CustomTextField(
                    controller: stockQuantityC,
                    labelText: 'Stock Quantity',
                    hintText: 'Enter stock quantity',
                    readOnly: true,
                    showLabelHeader: true,
                    suffixIcon: SuffixBox(text: "kg"),
                  ),
                  YBox(16),
                  CustomTextField(
                    controller: quantityPerSellingUnitC,
                    labelText: 'Quantity per Selling Unit',
                    hintText: 'Enter quantity per selling unit',
                    readOnly: true,
                    showLabelHeader: true,
                  ),
                  YBox(16),
                  CustomTextField(
                    controller: minimumOrderQuantityC,
                    labelText: 'Minimum Order Quantity',
                    hintText: 'Enter minimum order quantity',
                    readOnly: true,
                    showLabelHeader: true,
                  ),
                  YBox(16),
                  CustomTextField(
                    controller: weightPerSellingUnitC,
                    labelText: 'Weight per Selling Unit',
                    hintText: 'Enter weight per selling unit',
                    showLabelHeader: true,
                    keyboardType: TextInputType.number,
                    inputFormatters: [
                      FilteringTextInputFormatter.digitsOnly,
                    ],
                    suffixIcon: SuffixBox(text: "kg"),
                  ),
                  YBox(16),
                  CustomTextField(
                    controller: reorderLevelC,
                    labelText: 'Reorder Level',
                    hintText: 'Enter reorder level',
                    readOnly: true,
                    showLabelHeader: true,
                  ),
                  YBox(16),
                  CustomTextField(
                    controller: costPriceC,
                    labelText: 'Cost Price',
                    hintText: '00.00',
                    showLabelHeader: true,
                    prefixIcon: Padding(
                      padding: EdgeInsets.all(Sizer.radius(10)),
                      child: SvgPicture.asset(
                        AppSvgs.naira,
                        fit: BoxFit.cover,
                      ),
                    ),
                  ),
                  YBox(16),
                  CustomTextField(
                    controller: sellingPriceC,
                    labelText: 'Selling Price',
                    hintText: '00.00',
                    showLabelHeader: true,
                    prefixIcon: Padding(
                      padding: EdgeInsets.all(Sizer.radius(10)),
                      child: SvgPicture.asset(
                        AppSvgs.naira,
                        fit: BoxFit.cover,
                      ),
                    ),
                  ),
                  YBox(16),
                  CustomTextField(
                    controller: discountPriceC,
                    labelText: 'Discount Price',
                    optionalText: "(optional)",
                    hintText: '00.00',
                    showLabelHeader: true,
                    prefixIcon: Padding(
                      padding: EdgeInsets.all(Sizer.radius(10)),
                      child: SvgPicture.asset(
                        AppSvgs.naira,
                        fit: BoxFit.cover,
                      ),
                    ),
                  ),
                  YBox(16),
                  CustomTextField(
                    controller: productTagsC,
                    labelText: 'Product Tags',
                    hintText: 'Enter product tags',
                    showLabelHeader: true,
                  ),
                  Text(
                    "This will help customers find your product in the marketplace.",
                    style: textTheme.text14?.copyWith(
                      color: colorScheme.black45,
                    ),
                  ),
                  YBox(16),
                  CustomTextField(
                    controller: skuC,
                    labelText:
                        'Store Keeping Unit (SKU)                                    ',
                    hintText: 'TL-12346-IB',
                    showLabelHeader: true,
                  ),
                  YBox(16),
                  CustomTextField(
                    controller: productDescriptionC,
                    labelText: 'Description',
                    hintText: 'TL-12346-IB',
                    showLabelHeader: true,
                  ),
                  YBox(16),
                  Text(
                    "Product Images",
                    style: textTheme.text14,
                  ),
                  YBox(8),
                  InkWell(
                    onTap: () {
                      Navigator.pushNamed(context, RoutePath.viewUploadScreen);
                    },
                    child: SizedBox(
                      height: Sizer.height(104),
                      width: Sizer.screenWidth,
                      child: SvgPicture.asset(
                        AppSvgs.uploadImg,
                        fit: BoxFit.cover,
                      ),
                    ),
                  ),
                  YBox(4),
                  Text(
                    "Recommended file size is less than 2MB. JEPG, PNG formats only",
                    style: textTheme.text14?.copyWith(
                      color: colorScheme.black45,
                    ),
                  ),
                  YBox(32),
                  Row(
                    children: [
                      Expanded(
                        child: CustomBtn.withChild(
                          isOutline: true,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              SvgPicture.asset(
                                AppSvgs.trash,
                                height: Sizer.height(16),
                              ),
                              XBox(10),
                              Text(
                                "Remove",
                                style: textTheme.text16?.copyWith(
                                  color: AppColors.red2D,
                                ),
                              ),
                            ],
                          ),
                          onTap: () {},
                        ),
                      ),
                      XBox(16),
                      Expanded(
                        child: CustomBtn.solid(
                          text: "Save",
                          onTap: () {},
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ));
  }
}

class SuffixBox extends StatelessWidget {
  const SuffixBox({
    super.key,
    required this.text,
  });

  final String text;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(Sizer.radius(10)),
      decoration: BoxDecoration(
        border: Border(
          left: BorderSide(
            color: AppColors.neutral5,
          ),
        ),
      ),
      child: Text(text),
    );
  }
}
